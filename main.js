const { app } = require('electron');

// Import feature modules
const { createCommandWindow, toggleCommandWindow, destroyCommandWindow } = require('./features/window/commandWindow');
const { createTray, destroyTray } = require('./features/tray/systemTray');
const { registerShortcuts, unregisterAllShortcuts } = require('./features/shortcuts/globalShortcuts');
const { setupAppEvents } = require('./features/app/lifecycle');
const { toggleSettingsWindow, destroySettingsWindow } = require('./features/settings/settingsWindow');


// Proper quit function
function quitApp() {
  // Cleanup
  unregisterAllShortcuts();
  destroyTray();
  destroyCommandWindow();
  destroySettingsWindow();
  app.quit();
}

// App initialization
app.whenReady().then(() => {
  // Setup app lifecycle events
  const appInitialized = setupAppEvents(createCommandWindow, quitApp);
  if (!appInitialized) {
    return; // Another instance is already running
  }

  // Create main components
  createCommandWindow();
  createTray(toggleCommandWindow, quitApp);

  // Register global shortcuts
  registerShortcuts([
    {
      accelerator: 'CommandOrControl+Space',
      callback: toggleCommandWindow
    },
    {
      accelerator: 'CommandOrControl+.',
      callback: toggleSettingsWindow
    }
  ]);
});

