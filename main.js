const { app, BrowserWindow, Tray, Menu, nativeImage, globalShortcut, screen } = require('electron');
const path = require('path');

let commandWindow; // Floating command bar
let tray;

function createCommandWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // Calculate window position (centered horizontally, top third vertically)
  const windowWidth = 500;
  const windowHeight = 60;
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round(screenHeight * 0.2); // Position in top 20% of screen

  commandWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    show: false, // Start hidden so tray can control visibility
    frame: false, // Remove window frame for floating appearance
    transparent: true, // Enable transparency
    resizable: false, // Prevent resizing for consistent floating appearance
    alwaysOnTop: true, // Keep window on top like Raycast
    skipTaskbar: true, // Don't show in taskbar
    hasShadow: true, // Add shadow for floating effect
    vibrancy: 'under-window', // macOS vibrancy effect
    titleBarStyle: 'hidden', // Hide title bar
    minimizable: false, // Prevent minimize
    maximizable: false, // Prevent maximize
    fullscreenable: false, // Prevent fullscreen
    movable: true, // Prevent moving (stays centered)
    closable: false, // Prevent closing via window controls
    focusable: false, // Don't steal focus from active window
    acceptFirstMouse: false, // Don't accept first mouse click
    type: 'panel' // macOS panel type for floating behavior
  });

  commandWindow.loadFile('index.html');

  // Don't auto-hide on blur - let it float independently
  commandWindow.on('closed', () => {
    commandWindow = null;
  });

  // Prevent window from being minimized
  commandWindow.on('minimize', (event) => {
    event.preventDefault();
    commandWindow.hide();
  });
}



function createTray() {
  const iconPath = path.join(__dirname, 'menu-button.png');

  // Create native image and resize for tray (macOS prefers 16x16 or 22x22)
  const icon = nativeImage.createFromPath(iconPath);
  const resizedIcon = icon.resize({ width: 16, height: 16 });

  tray = new Tray(resizedIcon);
  tray.setToolTip('AgentZero');

  // Handle left-click to toggle command window visibility
  tray.on('click', () => {
    toggleCommandWindow();
  });

  // Handle right-click to show context menu
  tray.on('right-click', () => {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Quit',
        click: () => {
          quitApp();
        }
      }
    ]);
    tray.popUpContextMenu(contextMenu);
  });
}

function toggleCommandWindow() {
  if (commandWindow) {
    if (commandWindow.isVisible()) {
      hideCommandWindow();
    } else {
      showCommandWindow();
    }
  }
}

function showCommandWindow() {
  if (commandWindow) {
    commandWindow.showInactive(); // Show without stealing focus
    // Don't call focus() - let the active window keep focus
  }
}

function hideCommandWindow() {
  if (commandWindow) {
    commandWindow.hide();
  }
}



// App initialization
app.whenReady().then(() => {
  createCommandWindow();
  createTray();

  // Register global shortcut (Cmd+Space)
  globalShortcut.register('CommandOrControl+Space', () => {
    toggleCommandWindow();
  });

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createCommandWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  // The tray icon will allow users to reopen the window
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts
  globalShortcut.unregisterAll();
});

app.on('before-quit', () => {
  // Ensure proper cleanup
  if (commandWindow) {
    commandWindow.removeAllListeners('closed');
    commandWindow.close();
  }
  if (tray) {
    tray.destroy();
  }
});

// Proper quit function
function quitApp() {
  // Cleanup
  globalShortcut.unregisterAll();
  if (tray) {
    tray.destroy();
  }
  if (commandWindow) {
    commandWindow.close();
  }
  app.quit();
}

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    showWindow();
  });
}

