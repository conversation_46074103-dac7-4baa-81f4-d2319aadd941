<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Bar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: transparent;
            overflow: hidden;
            -webkit-app-region: drag; /* Make entire window draggable */
        }

        .command-bar {
            width: 100%;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: relative;
        }

        .search-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.6;
            flex-shrink: 0;
        }

        .command-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            -webkit-app-region: no-drag; /* Allow input interaction */
        }

        .command-input::placeholder {
            color: #999;
        }

        .shortcut-hint {
            font-size: 12px;
            color: #666;
            background: rgba(0, 0, 0, 0.05);
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 12px;
            flex-shrink: 0;
            -webkit-app-region: no-drag;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .command-bar {
                background: rgba(30, 30, 30, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .command-input {
                color: #fff;
            }

            .command-input::placeholder {
                color: #666;
            }

            .shortcut-hint {
                color: #ccc;
                background: rgba(255, 255, 255, 0.1);
            }
        }
    </style>
</head>
<body>
    <div class="command-bar">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
        </svg>
        <input
            type="text"
            class="command-input"
            placeholder="Type a command..."
            autofocus
        >
        <div class="shortcut-hint">⌘ Space</div>
    </div>

    <script>
        // Focus input when window becomes visible
        document.addEventListener('DOMContentLoaded', () => {
            const input = document.querySelector('.command-input');
            input.focus();

            // Handle escape key to hide window
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Send message to main process to hide window
                    window.close();
                }
            });

            // Handle enter key for command execution
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    const command = input.value.trim();
                    if (command) {
                        console.log('Command entered:', command);
                        // Here you would handle the command
                        input.value = '';
                    }
                }
            });
        });
    </script>
</body>
</html>
