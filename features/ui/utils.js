const { screen } = require('electron');

/**
 * Calculate centered window position
 * @param {number} windowWidth - Width of the window
 * @param {number} windowHeight - Height of the window
 * @param {number} verticalOffset - Vertical offset as percentage (0.0 to 1.0)
 * @returns {Object} Position object with x and y coordinates
 */
function getCenteredPosition(windowWidth, windowHeight, verticalOffset = 0.5) {
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round(screenHeight * verticalOffset);

  return { x, y };
}

/**
 * Common window configuration for floating windows
 * @param {Object} options - Window options
 * @returns {Object} BrowserWindow configuration
 */
function getFloatingWindowConfig(options = {}) {
  const defaults = {
    frame: false,
    transparent: true,
    resizable: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    hasShadow: true,
    vibrancy: 'under-window',
    titleBarStyle: 'hidden',
    minimizable: false,
    maximizable: false,
    fullscreenable: false,
    movable: true,
    closable: false,
    focusable: true,
    acceptFirstMouse: true,
    type: 'panel',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    }
  };

  return { ...defaults, ...options };
}

/**
 * Common window configuration for regular windows
 * @param {Object} options - Window options
 * @returns {Object} BrowserWindow configuration
 */
function getRegularWindowConfig(options = {}) {
  const defaults = {
    frame: true,
    transparent: false,
    resizable: true,
    alwaysOnTop: false,
    skipTaskbar: false,
    hasShadow: true,
    titleBarStyle: 'default',
    minimizable: true,
    maximizable: true,
    fullscreenable: true,
    movable: true,
    closable: true,
    focusable: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    }
  };

  return { ...defaults, ...options };
}

module.exports = {
  getCenteredPosition,
  getFloatingWindowConfig,
  getRegularWindowConfig
};
