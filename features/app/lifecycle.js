const { app, BrowserWindow } = require('electron');

function setupAppEvents(createCommandWindow, quitApp) {
  // Prevent multiple instances
  const gotTheLock = app.requestSingleInstanceLock();

  if (!gotTheLock) {
    app.quit();
    return false;
  }

  app.on('second-instance', () => {
    // Someone tried to run a second instance, show our window instead
    const { showCommandWindow } = require('../window/commandWindow');
    showCommandWindow();
  });

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createCommandWindow();
    }
  });

  app.on('window-all-closed', () => {
    // On macOS, keep app running even when all windows are closed
    // The tray icon will allow users to reopen the window
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });

  app.on('will-quit', () => {
    // Unregister all shortcuts
    const { unregisterAllShortcuts } = require('../shortcuts/globalShortcuts');
    unregisterAllShortcuts();
  });

  app.on('before-quit', () => {
    // Ensure proper cleanup
    if (quitApp) {
      quitApp();
    }
  });

  return true;
}

module.exports = {
  setupAppEvents
};
