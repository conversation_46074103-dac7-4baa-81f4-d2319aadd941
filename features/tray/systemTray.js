const { Tray, Menu, nativeImage, app } = require('electron');
const path = require('path');

let tray = null;

function createTray(onToggleCommand, onQuit) {
  const iconPath = path.join(__dirname, '../../menu-button.png');

  // Create native image and resize for tray (macOS prefers 16x16 or 22x22)
  const icon = nativeImage.createFromPath(iconPath);
  const resizedIcon = icon.resize({ width: 16, height: 16 });

  tray = new Tray(resizedIcon);
  tray.setToolTip('AgentZero');

  // Handle left-click to toggle command window visibility
  tray.on('click', () => {
    if (onToggleCommand) {
      onToggleCommand();
    }
  });

  // Handle right-click to show context menu
  tray.on('right-click', () => {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Quit',
        click: () => {
          if (onQuit) {
            onQuit();
          }
        }
      }
    ]);
    tray.popUpContextMenu(contextMenu);
  });

  return tray;
}

function getTray() {
  return tray;
}

function destroyTray() {
  if (tray) {
    tray.destroy();
    tray = null;
  }
}

module.exports = {
  createTray,
  getTray,
  destroyTray
};
