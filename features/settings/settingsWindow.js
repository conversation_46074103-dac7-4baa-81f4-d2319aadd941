const { BrowserWindow } = require('electron');
const { getCenteredPosition, getRegularWindowConfig } = require('../ui/utils');

let settingsWindow = null;

function createSettingsWindow() {
  if (settingsWindow) {
    settingsWindow.focus();
    return settingsWindow;
  }

  const windowWidth = 600;
  const windowHeight = 400;
  const { x, y } = getCenteredPosition(windowWidth, windowHeight, 0.5);

  const config = getRegularWindowConfig({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    show: false,
    title: 'AgentZero Settings'
  });

  settingsWindow = new BrowserWindow(config);

  settingsWindow.loadFile('features/settings/settings.html');

  settingsWindow.on('closed', () => {
    settingsWindow = null;
  });

  settingsWindow.show();
  return settingsWindow;
}

function getSettingsWindow() {
  return settingsWindow;
}

function toggleSettingsWindow() {
  if (settingsWindow) {
    if (settingsWindow.isVisible()) {
      settingsWindow.hide();
    } else {
      settingsWindow.show();
      settingsWindow.focus();
    }
  } else {
    createSettingsWindow();
  }
}

function destroySettingsWindow() {
  if (settingsWindow) {
    settingsWindow.close();
    settingsWindow = null;
  }
}

module.exports = {
  createSettingsWindow,
  getSettingsWindow,
  toggleSettingsWindow,
  destroySettingsWindow
};
