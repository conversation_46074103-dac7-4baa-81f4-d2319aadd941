const { globalShortcut } = require('electron');

let registeredShortcuts = [];

function registerShortcuts(shortcuts) {
  // Unregister any existing shortcuts first
  unregisterAllShortcuts();

  shortcuts.forEach(({ accelerator, callback }) => {
    const success = globalShortcut.register(accelerator, callback);
    if (success) {
      registeredShortcuts.push(accelerator);
      console.log(`Registered shortcut: ${accelerator}`);
    } else {
      console.error(`Failed to register shortcut: ${accelerator}`);
    }
  });
}

function unregisterAllShortcuts() {
  globalShortcut.unregisterAll();
  registeredShortcuts = [];
}

function isShortcutRegistered(accelerator) {
  return globalShortcut.isRegistered(accelerator);
}

module.exports = {
  registerShortcuts,
  unregisterAllShortcuts,
  isShortcutRegistered
};
