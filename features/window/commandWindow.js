const { BrowserWindow } = require('electron');
const { getCenteredPosition, getFloatingWindowConfig } = require('../ui/utils');

let commandWindow = null;

function createCommandWindow() {
  const windowWidth = 500;
  const windowHeight = 60;
  const { x, y } = getCenteredPosition(windowWidth, windowHeight, 0.2);

  const config = getFloatingWindowConfig({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    show: false // Start hidden so tray can control visibility
  });

  commandWindow = new BrowserWindow(config);

  commandWindow.loadFile('index.html');

  // Don't auto-hide on blur - let it float independently
  commandWindow.on('closed', () => {
    commandWindow = null;
  });

  // Prevent window from being minimized
  commandWindow.on('minimize', (event) => {
    event.preventDefault();
    commandWindow.hide();
  });

  return commandWindow;
}

function getCommandWindow() {
  return commandWindow;
}

function toggleCommandWindow() {
  if (commandWindow) {
    if (commandWindow.isVisible()) {
      hideCommandWindow();
    } else {
      showCommandWindow();
    }
  }
}

function showCommandWindow() {
  if (commandWindow) {
    commandWindow.show();
    commandWindow.focus(); // Focus for input
  }
}

function hideCommandWindow() {
  if (commandWindow) {
    commandWindow.hide();
  }
}

function destroyCommandWindow() {
  if (commandWindow) {
    commandWindow.removeAllListeners('closed');
    commandWindow.close();
    commandWindow = null;
  }
}

module.exports = {
  createCommandWindow,
  getCommandWindow,
  toggleCommandWindow,
  showCommandWindow,
  hideCommandWindow,
  destroyCommandWindow
};
